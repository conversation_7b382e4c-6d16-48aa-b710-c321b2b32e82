"""
Code Generation & Assembly Module for CodeQuilter.

This module implements the quality-first generation process that transforms
selected components into production-ready applications through intelligent
component integration and test-driven code generation.

Key Components:
- ComponentAPIAnalyzer: Tree-Sitter based component interface analysis
- ContextWeaver v2: Enhanced context engine with RAG capabilities
- AdapterGenerator: Core adapter generation engine
- TestGenerationEngine: Automatic test suite generation
- ValidationPipeline: Sandbox execution and validation
- CodeGenerationOrchestrator: Main orchestrator with TaskExecutionAgent integration

Strategic Principles:
- Quality-First Generation: No code is complete until it passes validation
- Component-Aware Generation: Precise integration based on actual APIs
- Specialized LLM Strategy: Multi-model routing for optimal quality and cost
- TaskExecutionAgent Native: Built with Plan-Do-Verify loop from day one
- Gemini's Three Principles: Execution rationale, activity instrumentation, post-execution actions
"""

from .component_api_analyzer import ComponentAPIAnalyzer
from .context_weaver import ContextWeaver
from .specialized_llm_client import SpecializedLLMClient
from .adapter_generator import AdapterGenerator

from .test_generation_engine import TestGeneration<PERSON>ngine, TestFramework, TestType
from .validation_pipeline import ValidationPipeline, ValidationGate, SandboxEnvironment
from .code_generation_orchestrator import (
    CodeGenerationOrchestrator, OrchestrationPhase, GenerationStrategy
)

__all__ = [
    "ComponentAPIAnalyzer",
    "ContextWeaver",
    "SpecializedLLMClient",
    "AdapterGenerator",
    "TestGenerationEngine",
    "TestFramework",
    "TestType",
    "ValidationPipeline",
    "ValidationGate",
    "SandboxEnvironment",
    "CodeGenerationOrchestrator",
    "OrchestrationPhase",
    "GenerationStrategy"
]
