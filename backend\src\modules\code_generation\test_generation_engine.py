"""
TestGenerationEngine - Comprehensive test generation for adapters and components.

Generates unit tests, integration tests, and mock scenarios for generated code
to ensure >90% test coverage targets and professional testing standards.

Key Features:
- Pattern-aware test generation using AdapterGenerator context
- Multiple testing framework support (pytest, jest, etc.)
- Comprehensive test coverage analysis
- Integration with ContextWeaver for test-specific prompts
- Quality-first test generation with validation
"""

import logging
import asyncio
import re
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime
from enum import Enum

from ...state.components import (
    ComponentAPI, AdapterCode, GenerationContext, CodeGenerationType,
    IntegrationComplexity, ComponentTestResult, ComponentTestStatus
)
from ...state.project_state import ProjectState
from .context_weaver import ContextWeaver
from .specialized_llm_client import SpecializedLLMClient

logger = logging.getLogger(__name__)


class TestFramework(Enum):
    """Supported testing frameworks"""
    PYTEST = "pytest"
    UNITTEST = "unittest"
    JEST = "jest"
    MOCHA = "mocha"
    JUNIT = "junit"
    GENERIC = "generic"


class TestType(Enum):
    """Types of tests that can be generated"""
    UNIT = "unit"
    INTEGRATION = "integration"
    MOCK = "mock"
    PERFORMANCE = "performance"
    SECURITY = "security"
    END_TO_END = "end_to_end"


class TestGenerationEngine:
    """
    Comprehensive test generation engine for adapters and components.
    
    Builds on AdapterGenerator's pattern-aware generation to create
    comprehensive test suites that ensure generated code quality.
    """
    
    def __init__(self, context_weaver: Optional[ContextWeaver] = None,
                 llm_client: Optional[SpecializedLLMClient] = None):
        """Initialize TestGenerationEngine with context and LLM clients"""
        # TODO: REPLACE_MOCK - Real integration with ContextWeaver and SpecializedLLMClient
        self.context_weaver = context_weaver or ContextWeaver()
        self.llm_client = llm_client or SpecializedLLMClient()
        
        # Test framework configurations
        self.framework_configs = self._initialize_framework_configs()
        
        # Test generation statistics
        self.generation_stats = {
            "total_test_suites_generated": 0,
            "successful_generations": 0,
            "framework_usage": {},
            "test_type_distribution": {},
            "average_coverage_achieved": 0.0,
            "average_generation_time": 0.0
        }
        
        # Test pattern templates
        self.test_patterns = self._initialize_test_patterns()
        
        # Coverage targets by test type
        self.coverage_targets = {
            TestType.UNIT: 0.95,
            TestType.INTEGRATION: 0.85,
            TestType.MOCK: 0.90,
            TestType.PERFORMANCE: 0.70,
            TestType.SECURITY: 0.80,
            TestType.END_TO_END: 0.75
        }
    
    def _initialize_framework_configs(self) -> Dict[str, Dict[str, Any]]:
        """Initialize testing framework configurations"""
        return {
            "pytest": {
                "file_pattern": "test_{name}.py",
                "import_statements": ["import pytest", "from unittest.mock import Mock, patch"],
                "test_decorator": "@pytest.mark.asyncio",
                "assertion_style": "assert",
                "fixture_decorator": "@pytest.fixture",
                "mock_framework": "unittest.mock",
                "async_support": True,
                "parametrize_support": True
            },
            "unittest": {
                "file_pattern": "test_{name}.py",
                "import_statements": ["import unittest", "from unittest.mock import Mock, patch"],
                "test_decorator": "",
                "assertion_style": "self.assert",
                "fixture_decorator": "def setUp(self):",
                "mock_framework": "unittest.mock",
                "async_support": False,
                "parametrize_support": False
            },
            "jest": {
                "file_pattern": "{name}.test.js",
                "import_statements": ["const { jest } = require('@jest/globals');"],
                "test_decorator": "",
                "assertion_style": "expect",
                "fixture_decorator": "beforeEach",
                "mock_framework": "jest",
                "async_support": True,
                "parametrize_support": True
            },
            "mocha": {
                "file_pattern": "{name}.test.js",
                "import_statements": ["const { expect } = require('chai');", "const sinon = require('sinon');"],
                "test_decorator": "",
                "assertion_style": "expect",
                "fixture_decorator": "beforeEach",
                "mock_framework": "sinon",
                "async_support": True,
                "parametrize_support": False
            },
            "junit": {
                "file_pattern": "{name}Test.java",
                "import_statements": ["import org.junit.jupiter.api.Test;", "import org.mockito.Mock;"],
                "test_decorator": "@Test",
                "assertion_style": "Assertions.assert",
                "fixture_decorator": "@BeforeEach",
                "mock_framework": "mockito",
                "async_support": False,
                "parametrize_support": True
            }
        }
    
    def _initialize_test_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize test pattern templates for different adapter types"""
        return {
            "rest_api": {
                "unit_tests": [
                    "test_adapter_initialization",
                    "test_request_formatting",
                    "test_response_parsing",
                    "test_error_handling",
                    "test_authentication_headers"
                ],
                "integration_tests": [
                    "test_end_to_end_api_call",
                    "test_authentication_flow",
                    "test_error_response_handling",
                    "test_timeout_scenarios"
                ],
                "mock_scenarios": [
                    "mock_successful_response",
                    "mock_authentication_failure",
                    "mock_network_timeout",
                    "mock_invalid_response_format"
                ]
            },
            "message_queue": {
                "unit_tests": [
                    "test_message_serialization",
                    "test_message_deserialization",
                    "test_queue_connection",
                    "test_message_publishing",
                    "test_message_consumption"
                ],
                "integration_tests": [
                    "test_end_to_end_message_flow",
                    "test_queue_durability",
                    "test_message_ordering",
                    "test_dead_letter_queue"
                ],
                "mock_scenarios": [
                    "mock_queue_connection_failure",
                    "mock_message_processing_error",
                    "mock_queue_full_scenario",
                    "mock_consumer_unavailable"
                ]
            },
            "database": {
                "unit_tests": [
                    "test_connection_establishment",
                    "test_query_execution",
                    "test_transaction_handling",
                    "test_connection_pooling",
                    "test_query_parameter_binding"
                ],
                "integration_tests": [
                    "test_database_operations",
                    "test_transaction_rollback",
                    "test_concurrent_access",
                    "test_connection_recovery"
                ],
                "mock_scenarios": [
                    "mock_database_connection_failure",
                    "mock_query_timeout",
                    "mock_transaction_deadlock",
                    "mock_connection_pool_exhaustion"
                ]
            },
            "authentication": {
                "unit_tests": [
                    "test_token_generation",
                    "test_token_validation",
                    "test_user_authentication",
                    "test_permission_checking",
                    "test_session_management"
                ],
                "integration_tests": [
                    "test_login_flow",
                    "test_token_refresh",
                    "test_logout_process",
                    "test_permission_enforcement"
                ],
                "mock_scenarios": [
                    "mock_invalid_credentials",
                    "mock_expired_token",
                    "mock_insufficient_permissions",
                    "mock_authentication_service_down"
                ]
            }
        }

    async def generate_test_suite(self, adapter_code: AdapterCode,
                                project_state: ProjectState,
                                test_types: Optional[List[TestType]] = None,
                                framework: Optional[TestFramework] = None) -> ComponentTestResult:
        """
        Generate comprehensive test suite for adapter code.

        Args:
            adapter_code: The adapter code to generate tests for
            project_state: Current project state with context
            test_types: Types of tests to generate (default: unit, integration, mock)
            framework: Testing framework to use (auto-detected if None)

        Returns:
            ComponentTestResult with generated test suite and metrics
        """
        start_time = datetime.now()

        try:
            # Determine test types if not specified
            if test_types is None:
                test_types = [TestType.UNIT, TestType.INTEGRATION, TestType.MOCK]

            # Determine testing framework
            if framework is None:
                framework = self._detect_testing_framework(adapter_code.language, project_state)

            logger.info(f"Generating test suite for {adapter_code.source_component} -> {adapter_code.target_component}")

            # Build test generation context
            context = self._build_test_context(adapter_code, project_state, test_types, framework)

            # Generate tests for each type
            generated_tests = {}
            total_coverage = 0.0

            for test_type in test_types:
                test_code = await self._generate_tests_for_type(
                    test_type, adapter_code, context
                )
                generated_tests[test_type.value] = test_code

                # Calculate coverage for this test type
                coverage = self._calculate_test_coverage(test_code, adapter_code, test_type)
                total_coverage += coverage * (1.0 / len(test_types))

            # Create test result with clean component names
            clean_source = re.sub(r'[^a-zA-Z0-9]', '_', adapter_code.source_component)
            clean_target = re.sub(r'[^a-zA-Z0-9]', '_', adapter_code.target_component)
            test_result = ComponentTestResult(
                component_name=f"{clean_source}_to_{clean_target}",
                test_type="comprehensive",
                status=ComponentTestStatus.PASSED,
                coverage_percentage=total_coverage * 100,
                test_files=[self._generate_test_filename(adapter_code, framework)]
            )

            # Add generated test code to adapter
            adapter_code.test_code = self._combine_test_files(generated_tests, framework)
            adapter_code.test_coverage = total_coverage

            # Track statistics
            generation_time = (datetime.now() - start_time).total_seconds()
            self._track_test_generation_stats(test_types, framework, total_coverage, generation_time)

            logger.info(f"Test suite generated with {total_coverage:.1%} coverage in {generation_time:.2f}s")
            return test_result

        except Exception as e:
            logger.error(f"Test generation failed: {str(e)}")
            return self._create_failed_test_result(adapter_code, str(e))

    def _detect_testing_framework(self, language: str, project_state: ProjectState) -> TestFramework:
        """Detect appropriate testing framework based on language and project context"""
        language_frameworks = {
            "python": TestFramework.PYTEST,
            "javascript": TestFramework.JEST,
            "typescript": TestFramework.JEST,
            "java": TestFramework.JUNIT
        }

        # Check if project has specific framework preferences
        if hasattr(project_state, 'testing_preferences'):
            preferred = project_state.testing_preferences.get('framework')
            if preferred and preferred in [f.value for f in TestFramework]:
                return TestFramework(preferred)

        # Default based on language
        return language_frameworks.get(language.lower(), TestFramework.GENERIC)

    def _build_test_context(self, adapter_code: AdapterCode, project_state: ProjectState,
                           test_types: List[TestType], framework: TestFramework) -> GenerationContext:
        """Build context for test generation"""
        context = GenerationContext(
            goal=f"Generate comprehensive tests for {adapter_code.pattern_type} adapter",
            generation_type=CodeGenerationType.TESTS,
            project_framework=project_state.project_brief,
            project_language=adapter_code.language
        )

        # Add test-specific context
        context.test_framework = framework.value
        context.test_types = [t.value for t in test_types]
        context.adapter_pattern = adapter_code.pattern_type
        context.source_component = adapter_code.source_component
        context.target_component = adapter_code.target_component

        # Add pattern-specific test templates
        if adapter_code.pattern_type in self.test_patterns:
            context.test_templates = self.test_patterns[adapter_code.pattern_type]

        return context

    async def _generate_tests_for_type(self, test_type: TestType, adapter_code: AdapterCode,
                                     context: GenerationContext) -> str:
        """Generate tests for a specific test type"""
        try:
            # Build test-specific prompt
            prompt = self._build_test_prompt(test_type, adapter_code, context)

            # Enhance context for this test type
            enhanced_context = self._enhance_context_for_test_type(context, test_type)

            # Generate test code using LLM
            generation_result = await self.llm_client.generate_code(
                context=enhanced_context,
                prompt=prompt,
                quality_level="production"
            )

            if generation_result.get("success", False):
                test_code = generation_result.get("generated_code", "")

                # Post-process test code
                test_code = self._post_process_test_code(test_code, test_type, context)

                return test_code
            else:
                # Fallback to template-based generation
                return self._generate_fallback_tests(test_type, adapter_code, context)

        except Exception as e:
            logger.warning(f"Failed to generate {test_type.value} tests: {str(e)}")
            return self._generate_fallback_tests(test_type, adapter_code, context)

    def _build_test_prompt(self, test_type: TestType, adapter_code: AdapterCode,
                          context: GenerationContext) -> str:
        """Build LLM prompt for specific test type"""
        framework_config = self.framework_configs.get(context.test_framework, {})

        prompt_parts = [
            f"Generate comprehensive {test_type.value} tests for the following adapter code:",
            f"",
            f"Adapter Pattern: {adapter_code.pattern_type}",
            f"Source Component: {adapter_code.source_component}",
            f"Target Component: {adapter_code.target_component}",
            f"Language: {adapter_code.language}",
            f"Testing Framework: {context.test_framework}",
            f"",
            f"Adapter Code:",
            f"```{adapter_code.language}",
            adapter_code.adapter_code,
            f"```",
            f"",
            f"Requirements:",
            f"- Use {context.test_framework} testing framework",
            f"- Follow {framework_config.get('assertion_style', 'standard')} assertion style",
            f"- Include proper imports: {', '.join(framework_config.get('import_statements', []))}",
            f"- Target coverage: {self.coverage_targets.get(test_type, 0.8):.0%}",
            f"- Include error handling and edge cases",
            f"- Use appropriate mocking for external dependencies"
        ]

        # Add test type specific requirements
        if test_type == TestType.UNIT:
            prompt_parts.extend([
                f"",
                f"Unit Test Specific Requirements:",
                f"- Test individual methods and functions in isolation",
                f"- Mock all external dependencies",
                f"- Test both success and failure scenarios",
                f"- Validate input/output transformations"
            ])
        elif test_type == TestType.INTEGRATION:
            prompt_parts.extend([
                f"",
                f"Integration Test Specific Requirements:",
                f"- Test end-to-end component interactions",
                f"- Use real or realistic test doubles",
                f"- Test configuration and setup",
                f"- Validate complete workflows"
            ])
        elif test_type == TestType.MOCK:
            prompt_parts.extend([
                f"",
                f"Mock Test Specific Requirements:",
                f"- Create comprehensive mock scenarios",
                f"- Test failure conditions and error handling",
                f"- Mock external service responses",
                f"- Test timeout and retry scenarios"
            ])

        # Add pattern-specific test scenarios
        if hasattr(context, 'test_templates') and context.test_templates:
            test_scenarios = context.test_templates.get(f"{test_type.value}_tests", [])
            if test_scenarios:
                prompt_parts.extend([
                    f"",
                    f"Include these test scenarios:",
                    *[f"- {scenario}" for scenario in test_scenarios]
                ])

        return "\n".join(prompt_parts)

    def _enhance_context_for_test_type(self, context: GenerationContext,
                                     test_type: TestType) -> GenerationContext:
        """Enhance context with test type specific information"""
        enhanced_context = GenerationContext(
            goal=f"Generate {test_type.value} tests",
            generation_type=CodeGenerationType.TESTS,
            project_framework=context.project_framework,
            project_language=context.project_language
        )

        # Copy relevant context
        for attr in ['test_framework', 'adapter_pattern', 'source_component', 'target_component']:
            if hasattr(context, attr):
                setattr(enhanced_context, attr, getattr(context, attr))

        # Add test type specific enhancements
        enhanced_context.test_type = test_type.value
        enhanced_context.coverage_target = self.coverage_targets.get(test_type, 0.8)
        enhanced_context.quality_level = "production"

        return enhanced_context

    def _post_process_test_code(self, test_code: str, test_type: TestType,
                               context: GenerationContext) -> str:
        """Post-process generated test code for quality and consistency"""
        # Remove any duplicate imports
        test_code = self._deduplicate_imports(test_code)

        # Ensure proper test naming conventions
        test_code = self._fix_test_naming(test_code, context.test_framework)

        # Add missing assertions if needed
        test_code = self._ensure_assertions(test_code, context.test_framework)

        # Format code consistently
        test_code = self._format_test_code(test_code, context.project_language)

        return test_code

    def _generate_fallback_tests(self, test_type: TestType, adapter_code: AdapterCode,
                                context: GenerationContext) -> str:
        """Generate basic fallback tests when LLM generation fails"""
        framework_config = self.framework_configs.get(context.test_framework, {})

        # TODO: REPLACE_MOCK - Implement template-based fallback generation
        # Clean component names for class naming
        clean_source = re.sub(r'[^a-zA-Z0-9]', '', adapter_code.source_component.title())
        clean_target = re.sub(r'[^a-zA-Z0-9]', '', adapter_code.target_component.title())

        fallback_template = f"""
{chr(10).join(framework_config.get('import_statements', []))}

class Test{clean_source}To{clean_target}Adapter:
    \"\"\"Fallback {test_type.value} tests for {adapter_code.pattern_type} adapter\"\"\"

    {framework_config.get('fixture_decorator', '@pytest.fixture')}
    def adapter_instance(self):
        \"\"\"Create adapter instance for testing\"\"\"
        # TODO: Initialize adapter with test configuration
        return None

    {framework_config.get('test_decorator', '')}
    def test_adapter_initialization(self, adapter_instance):
        \"\"\"Test adapter can be initialized\"\"\"
        {framework_config.get('assertion_style', 'assert')} adapter_instance is not None

    {framework_config.get('test_decorator', '')}
    def test_basic_functionality(self, adapter_instance):
        \"\"\"Test basic adapter functionality\"\"\"
        # TODO: Add specific tests for {adapter_code.pattern_type} pattern
        pass
"""

        return fallback_template.strip()

    def _calculate_test_coverage(self, test_code: str, adapter_code: AdapterCode,
                                test_type: TestType) -> float:
        """Calculate estimated test coverage for generated tests"""
        # TODO: REPLACE_MOCK - Implement real coverage analysis

        # Simple heuristic based on test count and adapter complexity
        test_count = len(re.findall(r'def test_', test_code))
        adapter_methods = len(re.findall(r'def \w+', adapter_code.adapter_code))

        if adapter_methods == 0:
            # If no methods found, check if there's any meaningful code
            if len(adapter_code.adapter_code.strip()) < 50:
                return 0.8  # Default coverage for simple adapters
            else:
                return 0.0  # No tests for complex code without identifiable methods

        # Calculate coverage based on test density
        coverage_ratio = min(test_count / max(adapter_methods, 1), 1.5)
        base_coverage = min(coverage_ratio * 0.6, 0.95)

        # Adjust based on test type
        type_multipliers = {
            TestType.UNIT: 1.0,
            TestType.INTEGRATION: 0.8,
            TestType.MOCK: 0.9,
            TestType.PERFORMANCE: 0.6,
            TestType.SECURITY: 0.7,
            TestType.END_TO_END: 0.7
        }

        return base_coverage * type_multipliers.get(test_type, 0.8)

    def _combine_test_files(self, generated_tests: Dict[str, str],
                           framework: TestFramework) -> str:
        """Combine multiple test types into a single test file"""
        framework_config = self.framework_configs.get(framework.value, {})

        combined_parts = []

        # Add imports (deduplicated)
        all_imports = set()
        for test_code in generated_tests.values():
            imports = re.findall(r'^(import .*|from .* import .*)$', test_code, re.MULTILINE)
            all_imports.update(imports)

        combined_parts.extend(sorted(all_imports))
        combined_parts.append("")

        # Add test classes/functions
        for test_type, test_code in generated_tests.items():
            # Remove imports from individual test code
            clean_code = re.sub(r'^(import .*|from .* import .*)$', '', test_code, flags=re.MULTILINE)
            clean_code = re.sub(r'\n\n+', '\n\n', clean_code).strip()

            if clean_code:
                combined_parts.append(f"# {test_type.upper()} TESTS")
                combined_parts.append(clean_code)
                combined_parts.append("")

        return "\n".join(combined_parts)

    def _generate_test_filename(self, adapter_code: AdapterCode,
                               framework: TestFramework) -> str:
        """Generate appropriate test filename"""
        framework_config = self.framework_configs.get(framework.value, {})
        pattern = framework_config.get("file_pattern", "test_{name}.py")

        # Create clean name from adapter components
        clean_source = re.sub(r'[^a-zA-Z0-9]', '_', adapter_code.source_component.lower())
        clean_target = re.sub(r'[^a-zA-Z0-9]', '_', adapter_code.target_component.lower())
        adapter_name = f"{clean_source}_to_{clean_target}_adapter"

        return pattern.format(name=adapter_name)

    def _deduplicate_imports(self, test_code: str) -> str:
        """Remove duplicate import statements"""
        lines = test_code.split('\n')
        imports = set()
        non_imports = []

        for line in lines:
            if line.strip().startswith(('import ', 'from ')):
                imports.add(line.strip())
            else:
                non_imports.append(line)

        # Combine deduplicated imports with rest of code
        result = list(sorted(imports)) + [''] + non_imports
        return '\n'.join(result)

    def _fix_test_naming(self, test_code: str, framework: str) -> str:
        """Ensure test methods follow naming conventions"""
        if framework in ['pytest', 'unittest']:
            # Ensure test methods start with 'test_'
            test_code = re.sub(r'def ((?!test_)\w+_test\w*)\(', r'def test_\1(', test_code)

        return test_code

    def _ensure_assertions(self, test_code: str, framework: str) -> str:
        """Add basic assertions to test methods that lack them"""
        # TODO: REPLACE_MOCK - Implement intelligent assertion detection and addition
        return test_code

    def _format_test_code(self, test_code: str, language: str) -> str:
        """Apply basic code formatting"""
        # TODO: REPLACE_MOCK - Implement language-specific formatting
        # For now, just clean up excessive whitespace
        test_code = re.sub(r'\n\s*\n\s*\n', '\n\n', test_code)
        return test_code.strip()

    def _create_failed_test_result(self, adapter_code: AdapterCode, error_message: str) -> ComponentTestResult:
        """Create test result for failed test generation"""
        clean_source = re.sub(r'[^a-zA-Z0-9]', '_', adapter_code.source_component)
        clean_target = re.sub(r'[^a-zA-Z0-9]', '_', adapter_code.target_component)
        return ComponentTestResult(
            component_name=f"{clean_source}_to_{clean_target}",
            test_type="generation_failed",
            status=ComponentTestStatus.FAILED,
            error_message=error_message,
            coverage_percentage=0.0
        )

    def _track_test_generation_stats(self, test_types: List[TestType], framework: TestFramework,
                                   coverage: float, generation_time: float) -> None:
        """Track test generation statistics"""
        self.generation_stats["total_test_suites_generated"] += 1
        self.generation_stats["successful_generations"] += 1

        # Track framework usage
        framework_key = framework.value
        if framework_key not in self.generation_stats["framework_usage"]:
            self.generation_stats["framework_usage"][framework_key] = 0
        self.generation_stats["framework_usage"][framework_key] += 1

        # Track test type distribution
        for test_type in test_types:
            type_key = test_type.value
            if type_key not in self.generation_stats["test_type_distribution"]:
                self.generation_stats["test_type_distribution"][type_key] = 0
            self.generation_stats["test_type_distribution"][type_key] += 1

        # Update averages
        total_suites = self.generation_stats["total_test_suites_generated"]
        current_avg_coverage = self.generation_stats["average_coverage_achieved"]
        current_avg_time = self.generation_stats["average_generation_time"]

        # Calculate new averages
        self.generation_stats["average_coverage_achieved"] = (
            (current_avg_coverage * (total_suites - 1) + coverage) / total_suites
        )
        self.generation_stats["average_generation_time"] = (
            (current_avg_time * (total_suites - 1) + generation_time) / total_suites
        )

    def get_generation_statistics(self) -> Dict[str, Any]:
        """Get comprehensive test generation statistics"""
        stats = self.generation_stats.copy()

        # Add success rate
        total = stats["total_test_suites_generated"]
        successful = stats["successful_generations"]
        stats["success_rate"] = successful / total if total > 0 else 0.0

        # Add supported frameworks and test types
        stats["supported_frameworks"] = [f.value for f in TestFramework]
        stats["supported_test_types"] = [t.value for t in TestType]

        # Add coverage targets
        stats["coverage_targets"] = {t.value: target for t, target in self.coverage_targets.items()}

        return stats

    def get_framework_recommendations(self, language: str, project_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get testing framework recommendations for a language and project"""
        recommendations = []

        # Language-specific recommendations
        if language.lower() == "python":
            recommendations.extend([
                {
                    "framework": "pytest",
                    "suitability_score": 0.9,
                    "reasons": ["Most popular Python testing framework", "Excellent fixture support", "Great async support"],
                    "setup_complexity": "low"
                },
                {
                    "framework": "unittest",
                    "suitability_score": 0.7,
                    "reasons": ["Built into Python standard library", "No additional dependencies"],
                    "setup_complexity": "minimal"
                }
            ])
        elif language.lower() in ["javascript", "typescript"]:
            recommendations.extend([
                {
                    "framework": "jest",
                    "suitability_score": 0.9,
                    "reasons": ["Comprehensive testing solution", "Built-in mocking", "Snapshot testing"],
                    "setup_complexity": "low"
                },
                {
                    "framework": "mocha",
                    "suitability_score": 0.7,
                    "reasons": ["Flexible and extensible", "Large ecosystem", "Good async support"],
                    "setup_complexity": "medium"
                }
            ])
        elif language.lower() == "java":
            recommendations.append({
                "framework": "junit",
                "suitability_score": 0.9,
                "reasons": ["Industry standard for Java", "Excellent IDE integration", "Rich assertion library"],
                "setup_complexity": "low"
            })

        # Sort by suitability score
        recommendations.sort(key=lambda x: x["suitability_score"], reverse=True)
        return recommendations
