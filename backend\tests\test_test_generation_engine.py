"""
Tests for TestGenerationEngine - Comprehensive test generation for adapters and components.

Tests the test generation functionality, framework support, and coverage analysis.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from backend.src.modules.code_generation.test_generation_engine import (
    CodeTestGenerationEngine, TestingFramework, TestingType
)
from backend.src.modules.code_generation.context_weaver import Context<PERSON>eaver
from backend.src.modules.code_generation.specialized_llm_client import SpecializedLLMClient
from backend.src.state.components import (
    AdapterCode, GenerationContext, CodeGenerationType,
    IntegrationComplexity, ComponentTestResult, ComponentTestStatus
)
from backend.src.state.project_state import ProjectState, ProjectStatus


class TestCodeTestGenerationEngine:
    """Test TestGenerationEngine functionality"""
    
    @pytest.fixture
    def mock_context_weaver(self):
        """Create mock ContextWeaver"""
        mock = Mock(spec=ContextWeaver)
        mock.build_generation_context.return_value = GenerationContext(
            goal="Generate tests",
            generation_type=CodeGenerationType.TESTS,
            project_framework="fastapi",
            project_language="python"
        )
        return mock
    
    @pytest.fixture
    def mock_llm_client(self):
        """Create mock SpecializedLLMClient"""
        mock = Mock(spec=SpecializedLLMClient)
        mock.generate_code = AsyncMock(return_value={
            "success": True,
            "generated_code": """
import pytest
from unittest.mock import Mock, patch

class TestAuthAdapter:
    @pytest.fixture
    def auth_adapter(self):
        return AuthAdapter()
    
    def test_authenticate_success(self, auth_adapter):
        result = auth_adapter.authenticate("valid_token")
        assert result is True
    
    def test_authenticate_failure(self, auth_adapter):
        result = auth_adapter.authenticate("invalid_token")
        assert result is False
""",
            "model_used": "claude-3-sonnet",
            "token_usage": 200,
            "cost_estimate": 0.20
        })
        return mock
    
    @pytest.fixture
    def test_engine(self, mock_context_weaver, mock_llm_client):
        """Create CodeTestGenerationEngine instance for testing"""
        return CodeTestGenerationEngine(
            context_weaver=mock_context_weaver,
            llm_client=mock_llm_client
        )
    
    @pytest.fixture
    def sample_adapter_code(self):
        """Create sample adapter code for testing"""
        return AdapterCode(
            source_component="auth-service",
            target_component="user-service",
            pattern_type="authentication",
            integration_complexity=IntegrationComplexity.MEDIUM,
            adapter_code="""
class AuthAdapter:
    def __init__(self, auth_service, user_service):
        self.auth_service = auth_service
        self.user_service = user_service
    
    def authenticate(self, token):
        if self.auth_service.validate_token(token):
            return True
        return False
    
    def get_user_info(self, token):
        if self.authenticate(token):
            return self.user_service.get_user_by_token(token)
        return None
""",
            file_path="auth_adapter.py",
            language="python"
        )
    
    @pytest.fixture
    def sample_project_state(self):
        """Create sample project state"""
        return ProjectState(
            session_id="test-session",
            project_name="Test Project",
            project_brief="Authentication integration project",
            target_patterns=["authentication"],
            status=ProjectStatus.QUILTING
        )
    
    def test_framework_configs_initialization(self, test_engine):
        """Test framework configurations are properly initialized"""
        configs = test_engine.framework_configs
        
        # Verify all expected frameworks are present
        expected_frameworks = ["pytest", "unittest", "jest", "mocha", "junit"]
        for framework in expected_frameworks:
            assert framework in configs
            
            # Verify framework structure
            config = configs[framework]
            assert "file_pattern" in config
            assert "import_statements" in config
            assert "assertion_style" in config
            assert "mock_framework" in config
            assert "async_support" in config
    
    def test_test_patterns_initialization(self, test_engine):
        """Test pattern templates are properly initialized"""
        patterns = test_engine.test_patterns
        
        # Verify all expected patterns are present
        expected_patterns = ["rest_api", "message_queue", "database", "authentication"]
        for pattern in expected_patterns:
            assert pattern in patterns
            
            # Verify pattern structure
            pattern_config = patterns[pattern]
            assert "unit_tests" in pattern_config
            assert "integration_tests" in pattern_config
            assert "mock_scenarios" in pattern_config
            assert len(pattern_config["unit_tests"]) > 0
    
    def test_coverage_targets_initialization(self, test_engine):
        """Test coverage targets are properly set"""
        targets = test_engine.coverage_targets
        
        # Verify all test types have targets
        for test_type in TestingType:
            assert test_type in targets
            assert 0.0 <= targets[test_type] <= 1.0

        # Verify unit tests have highest target
        assert targets[TestingType.UNIT] >= targets[TestingType.INTEGRATION]
    
    def test_detect_testing_framework_python(self, test_engine, sample_project_state):
        """Test framework detection for Python"""
        framework = test_engine._detect_testing_framework("python", sample_project_state)
        assert framework == TestingFramework.PYTEST

    def test_detect_testing_framework_javascript(self, test_engine, sample_project_state):
        """Test framework detection for JavaScript"""
        framework = test_engine._detect_testing_framework("javascript", sample_project_state)
        assert framework == TestingFramework.JEST

        framework = test_engine._detect_testing_framework("typescript", sample_project_state)
        assert framework == TestingFramework.JEST

    def test_detect_testing_framework_java(self, test_engine, sample_project_state):
        """Test framework detection for Java"""
        framework = test_engine._detect_testing_framework("java", sample_project_state)
        assert framework == TestingFramework.JUNIT

    def test_detect_testing_framework_unknown(self, test_engine, sample_project_state):
        """Test framework detection for unknown language"""
        framework = test_engine._detect_testing_framework("unknown", sample_project_state)
        assert framework == TestingFramework.GENERIC

    def test_build_test_context(self, test_engine, sample_adapter_code, sample_project_state):
        """Test test context building"""
        test_types = [TestingType.UNIT, TestingType.INTEGRATION]
        framework = TestingFramework.PYTEST

        context = test_engine._build_test_context(
            sample_adapter_code, sample_project_state, test_types, framework
        )

        assert context.generation_type == CodeGenerationType.TESTS
        assert context.test_framework == "pytest"
        assert context.test_types == ["unit", "integration"]
        assert context.adapter_pattern == "authentication"
        assert context.source_component == "auth-service"
        assert context.target_component == "user-service"
        assert hasattr(context, 'test_templates')

    @pytest.mark.asyncio
    async def test_generate_test_suite_success(self, test_engine, sample_adapter_code, sample_project_state):
        """Test successful test suite generation"""
        result = await test_engine.generate_test_suite(
            sample_adapter_code,
            sample_project_state,
            test_types=[TestingType.UNIT, TestingType.INTEGRATION],
            framework=TestingFramework.PYTEST
        )

        # Verify result structure
        assert isinstance(result, ComponentTestResult)
        assert result.component_name == "auth_service_to_user_service"
        assert result.test_type == "comprehensive"
        assert result.status == ComponentTestStatus.PASSED
        assert result.coverage_percentage > 0
        assert len(result.test_files) > 0

        # Verify adapter code was updated
        assert len(sample_adapter_code.test_code) > 0
        assert sample_adapter_code.test_coverage > 0

    @pytest.mark.asyncio
    async def test_generate_test_suite_default_params(self, test_engine, sample_adapter_code, sample_project_state):
        """Test test suite generation with default parameters"""
        result = await test_engine.generate_test_suite(
            sample_adapter_code,
            sample_project_state
        )

        assert isinstance(result, ComponentTestResult)
        assert result.status == ComponentTestStatus.PASSED

        # Should use default test types (unit, integration, mock)
        assert "unit" in sample_adapter_code.test_code.lower()
        assert "integration" in sample_adapter_code.test_code.lower() or "mock" in sample_adapter_code.test_code.lower()

    def test_build_test_prompt_unit_tests(self, test_engine, sample_adapter_code):
        """Test test prompt building for unit tests"""
        context = GenerationContext(
            goal="Generate unit tests",
            generation_type=CodeGenerationType.TESTS,
            project_language="python"
        )
        context.test_framework = "pytest"

        prompt = test_engine._build_test_prompt(TestingType.UNIT, sample_adapter_code, context)

        assert "unit tests" in prompt.lower()
        assert "pytest" in prompt.lower()
        assert "authentication" in prompt.lower()
        assert "auth-service" in prompt
        assert "user-service" in prompt
        assert "mock all external dependencies" in prompt.lower()
        assert "test individual methods" in prompt.lower()

    def test_build_test_prompt_integration_tests(self, test_engine, sample_adapter_code):
        """Test test prompt building for integration tests"""
        context = GenerationContext(
            goal="Generate integration tests",
            generation_type=CodeGenerationType.TESTS,
            project_language="python"
        )
        context.test_framework = "pytest"

        prompt = test_engine._build_test_prompt(TestingType.INTEGRATION, sample_adapter_code, context)

        assert "integration tests" in prompt.lower()
        assert "end-to-end" in prompt.lower()
        assert "complete workflows" in prompt.lower()

    def test_enhance_context_for_test_type(self, test_engine):
        """Test context enhancement for specific test types"""
        base_context = GenerationContext(
            goal="Generate tests",
            generation_type=CodeGenerationType.TESTS,
            project_language="python"
        )
        base_context.test_framework = "pytest"

        enhanced = test_engine._enhance_context_for_test_type(base_context, TestingType.UNIT)

        assert enhanced.test_type == "unit"
        assert enhanced.coverage_target == test_engine.coverage_targets[TestingType.UNIT]
        assert enhanced.quality_level == "production"
        assert enhanced.test_framework == "pytest"

    def test_calculate_test_coverage_basic(self, test_engine, sample_adapter_code):
        """Test basic test coverage calculation"""
        test_code = """
def test_authenticate_success():
    pass

def test_authenticate_failure():
    pass

def test_get_user_info():
    pass
"""

        coverage = test_engine._calculate_test_coverage(test_code, sample_adapter_code, TestingType.UNIT)

        assert 0.0 <= coverage <= 1.0
        assert coverage > 0.5  # Should have reasonable coverage with 3 tests for 2 methods

    def test_calculate_test_coverage_no_tests(self, test_engine, sample_adapter_code):
        """Test coverage calculation with no tests"""
        test_code = "# No tests here"

        coverage = test_engine._calculate_test_coverage(test_code, sample_adapter_code, TestingType.UNIT)

        # Should return 0.0 since there are no tests and the adapter has meaningful code
        assert coverage == 0.0

    def test_generate_test_filename_pytest(self, test_engine, sample_adapter_code):
        """Test test filename generation for pytest"""
        filename = test_engine._generate_test_filename(sample_adapter_code, TestingFramework.PYTEST)

        assert filename == "test_auth_service_to_user_service_adapter.py"

    def test_generate_test_filename_jest(self, test_engine, sample_adapter_code):
        """Test test filename generation for Jest"""
        filename = test_engine._generate_test_filename(sample_adapter_code, TestingFramework.JEST)

        assert filename == "auth_service_to_user_service_adapter.test.js"

    def test_generate_test_filename_junit(self, test_engine, sample_adapter_code):
        """Test test filename generation for JUnit"""
        filename = test_engine._generate_test_filename(sample_adapter_code, TestingFramework.JUNIT)

        assert filename == "auth_service_to_user_service_adapterTest.java"

    def test_deduplicate_imports(self, test_engine):
        """Test import deduplication"""
        test_code = """
import pytest
from unittest.mock import Mock
import pytest
from unittest.mock import patch
import os

def test_something():
    pass
"""

        result = test_engine._deduplicate_imports(test_code)

        # Should have unique imports
        assert result.count("import pytest") == 1
        assert result.count("from unittest.mock import Mock") == 1
        assert "from unittest.mock import patch" in result
        assert "def test_something():" in result

    def test_fix_test_naming_pytest(self, test_engine):
        """Test test method naming fixes for pytest"""
        test_code = """
def authenticate_test():
    pass

def user_info_test():
    pass

def test_already_correct():
    pass
"""

        result = test_engine._fix_test_naming(test_code, "pytest")

        assert "def test_authenticate_test():" in result
        assert "def test_user_info_test():" in result
        assert "def test_already_correct():" in result

    def test_combine_test_files(self, test_engine):
        """Test combining multiple test types into single file"""
        generated_tests = {
            "unit": """
import pytest
from unittest.mock import Mock

class TestUnit:
    def test_unit_method(self):
        pass
""",
            "integration": """
import pytest
from unittest.mock import patch

class TestIntegration:
    def test_integration_method(self):
        pass
"""
        }

        result = test_engine._combine_test_files(generated_tests, TestingFramework.PYTEST)

        # Should have deduplicated imports
        assert result.count("import pytest") == 1
        assert "from unittest.mock import Mock" in result
        assert "from unittest.mock import patch" in result

        # Should have both test classes
        assert "# UNIT TESTS" in result
        assert "# INTEGRATION TESTS" in result
        assert "class TestUnit:" in result
        assert "class TestIntegration:" in result

    def test_generate_fallback_tests(self, test_engine, sample_adapter_code):
        """Test fallback test generation"""
        context = GenerationContext(
            goal="Generate tests",
            generation_type=CodeGenerationType.TESTS,
            project_language="python"
        )
        context.test_framework = "pytest"

        result = test_engine._generate_fallback_tests(TestingType.UNIT, sample_adapter_code, context)

        assert "import pytest" in result
        assert "TestAuthServiceToUserServiceAdapter" in result
        assert "def test_adapter_initialization" in result
        assert "def test_basic_functionality" in result

    def test_track_test_generation_stats(self, test_engine):
        """Test generation statistics tracking"""
        initial_total = test_engine.generation_stats["total_test_suites_generated"]

        test_types = [TestingType.UNIT, TestingType.INTEGRATION]
        framework = TestingFramework.PYTEST
        coverage = 0.85
        generation_time = 2.5

        test_engine._track_test_generation_stats(test_types, framework, coverage, generation_time)

        stats = test_engine.generation_stats
        assert stats["total_test_suites_generated"] == initial_total + 1
        assert stats["successful_generations"] == initial_total + 1
        assert stats["framework_usage"]["pytest"] == 1
        assert stats["test_type_distribution"]["unit"] == 1
        assert stats["test_type_distribution"]["integration"] == 1
        assert stats["average_coverage_achieved"] > 0
        assert stats["average_generation_time"] > 0

    def test_get_generation_statistics(self, test_engine):
        """Test generation statistics retrieval"""
        # Add some mock stats
        test_engine.generation_stats["total_test_suites_generated"] = 10
        test_engine.generation_stats["successful_generations"] = 8
        test_engine.generation_stats["average_coverage_achieved"] = 0.85

        stats = test_engine.get_generation_statistics()

        assert stats["success_rate"] == 0.8
        assert "supported_frameworks" in stats
        assert "supported_test_types" in stats
        assert "coverage_targets" in stats
        assert len(stats["supported_frameworks"]) > 0
        assert len(stats["supported_test_types"]) > 0

    def test_get_framework_recommendations_python(self, test_engine):
        """Test framework recommendations for Python"""
        recommendations = test_engine.get_framework_recommendations("python", {})

        assert len(recommendations) > 0

        # Should be sorted by suitability score
        scores = [rec["suitability_score"] for rec in recommendations]
        assert scores == sorted(scores, reverse=True)

        # pytest should be top recommendation for Python
        top_rec = recommendations[0]
        assert top_rec["framework"] == "pytest"
        assert top_rec["suitability_score"] > 0.8
        assert "reasons" in top_rec
        assert "setup_complexity" in top_rec

    def test_get_framework_recommendations_javascript(self, test_engine):
        """Test framework recommendations for JavaScript"""
        recommendations = test_engine.get_framework_recommendations("javascript", {})

        assert len(recommendations) > 0

        # Jest should be top recommendation for JavaScript
        top_rec = recommendations[0]
        assert top_rec["framework"] == "jest"
        assert top_rec["suitability_score"] > 0.8

    def test_get_framework_recommendations_java(self, test_engine):
        """Test framework recommendations for Java"""
        recommendations = test_engine.get_framework_recommendations("java", {})

        assert len(recommendations) > 0

        # JUnit should be recommendation for Java
        top_rec = recommendations[0]
        assert top_rec["framework"] == "junit"
        assert top_rec["suitability_score"] > 0.8

    @pytest.mark.asyncio
    async def test_generate_test_suite_llm_failure(self, test_engine, sample_adapter_code, sample_project_state):
        """Test test suite generation when LLM fails"""
        # Mock LLM to fail
        test_engine.llm_client.generate_code = AsyncMock(return_value={
            "success": False,
            "error": "LLM generation failed"
        })

        result = await test_engine.generate_test_suite(
            sample_adapter_code,
            sample_project_state
        )

        # Should still succeed with fallback tests
        assert isinstance(result, ComponentTestResult)
        assert result.status == ComponentTestStatus.PASSED
        assert len(sample_adapter_code.test_code) > 0
        assert "fallback" in sample_adapter_code.test_code.lower() or "test_" in sample_adapter_code.test_code

    def test_create_failed_test_result(self, test_engine, sample_adapter_code):
        """Test failed test result creation"""
        error_message = "Test generation failed due to invalid input"

        result = test_engine._create_failed_test_result(sample_adapter_code, error_message)

        assert isinstance(result, ComponentTestResult)
        assert result.status == ComponentTestStatus.FAILED
        assert result.error_message == error_message
        assert result.coverage_percentage == 0.0
        assert result.component_name == "auth_service_to_user_service"
